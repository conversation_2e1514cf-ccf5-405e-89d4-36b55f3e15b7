#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试一键粘贴自动解析功能
"""

import requests
import json
import pyperclip

def test_clipboard_api():
    """测试剪切板API"""
    try:
        # 读取测试数据
        with open('测试数据.json', 'r', encoding='utf-8') as f:
            test_data = f.read()
        
        # 设置剪切板内容
        pyperclip.copy(test_data)
        print("✓ 测试数据已复制到剪切板")
        
        # 测试获取剪切板内容的API
        response = requests.get('http://localhost:8726/api/get_clipboard')
        result = response.json()
        
        if result['success']:
            print("✓ 剪切板API工作正常")
            
            # 验证内容是否正确
            clipboard_content = result['content']
            try:
                data = json.loads(clipboard_content)
                if data and data.get('result') and data['result'].get('subOrderForSupplierList'):
                    first_orders = [order for order in data['result']['subOrderForSupplierList'] if order.get('isFirst')]
                    print(f"✓ JSON格式验证通过，包含 {len(first_orders)} 个首单")
                    return True
                else:
                    print("× JSON结构不符合预期")
                    return False
            except json.JSONDecodeError as e:
                print(f"× JSON解析失败: {e}")
                return False
        else:
            print(f"× 剪切板API失败: {result.get('message', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"× 测试失败: {e}")
        return False

def test_parse_api():
    """测试解析API"""
    try:
        with open('测试数据.json', 'r', encoding='utf-8') as f:
            test_data = f.read()
        
        response = requests.post('http://localhost:8726/api/parse_json', 
                               json={'json_data': test_data},
                               headers={'Content-Type': 'application/json'})
        
        result = response.json()
        
        if result['success']:
            print(f"✓ 解析API工作正常，处理了 {len(result['products'])} 个商品")
            return True
        else:
            print(f"× 解析API失败: {result.get('message', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"× 解析API测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试一键粘贴自动解析功能...")
    print("=" * 50)
    
    # 测试剪切板API
    print("1. 测试剪切板API...")
    clipboard_ok = test_clipboard_api()
    
    print("\n2. 测试解析API...")
    parse_ok = test_parse_api()
    
    print("\n" + "=" * 50)
    if clipboard_ok and parse_ok:
        print("✅ 所有测试通过！功能应该正常工作。")
        print("现在可以在浏览器中点击'一键粘贴'按钮测试自动解析功能。")
    else:
        print("❌ 部分测试失败，请检查应用程序状态。")
